# 材质透明度控制功能

## 功能描述
在控制面板中添加了材质透明度控制滑块，用户可以通过滑块调整所有FDS材质的透明度。

## 实现的更改

### 1. ControlPanel.vue 更新
- 添加了"材质控制"面板部分
- 包含材质透明度滑块控件
- 滑块范围：0-100%，步长5%
- 实时显示当前透明度百分比

### 2. App.vue 更新
- 添加了 `materialTransparency` 状态管理
- 添加了 `viewEngine` 引用
- 实现了 `handleMaterialTransparencyChange` 方法
- 将必要的props传递给ControlPanel组件

### 3. Scene3D.vue 更新
- 添加了 `getViewEngine()` 方法
- 在 `defineExpose` 中暴露了 `getViewEngine` 方法

### 4. ViewEngine (render/index.js) 更新
- 改进了 `setFdsTransparency()` 方法
- 现在控制所有FDS材质的透明度（不仅仅是墙和窗）
- 确保材质启用透明度并标记需要更新

## 使用方法
1. 启动应用后，右侧控制面板会显示"材质控制"部分
2. 使用"材质透明度"滑块调整透明度
3. 滑块值从0%（完全透明）到100%（完全不透明）
4. 实时预览透明度变化效果

## 技术细节
- 使用Element Plus的el-slider组件
- 响应式状态管理，支持props和事件通信
- 统一的材质管理，确保所有FDS对象同步更新
- 优化的渲染性能，只在需要时更新材质

## 测试建议
1. 验证滑块是否正常显示和响应
2. 测试透明度变化是否实时生效
3. 确认所有FDS对象都受到透明度控制影响
4. 检查性能是否正常，无卡顿现象
