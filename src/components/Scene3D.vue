<template>
	<div
		class="scene-3d"
		ref="scene3DContainer"
	>
		<!-- 3D场景主内容区 -->
		<div class="scene-main-content"></div>

		<!-- 人员详情面板 -->
		<PersonDetailPanel
			:visible="showPersonDetailPanel"
			:person-data="selectedPersonData"
			@close="showPersonDetailPanel = false"
		/>

		<!-- 加载由全局LoadingOverlay组件处理 -->
	</div>

	<!-- 右键菜单 -->
	<div
		class="context-menu normal-card"
		v-if="showContextMenu"
		:style="{ top: contextMenuPos.y + 'px', left: contextMenuPos.x + 'px' }"
	>
		<div class="context-menu-header">
			<div class="context-menu-title">{{ selectedNodeLabel }}</div>
		</div>
		<div class="context-menu-content">
			<div
				class="context-menu-item"
				@click="hideSelectedObject"
			>
				<el-icon><Hide /></el-icon>
				<span>隐藏对象</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, nextTick } from 'vue';
	import { ViewEngine } from '../render';
	import ControlPanel from './ControlPanel.vue';
	import PlaybackControls from './PlaybackControls.vue';
	import PersonDetailPanel from './PersonDetailPanel.vue';
	import { Hide } from '@element-plus/icons-vue';

	import * as THREE from 'three';

	const scene3DContainer = ref(null);
	const isInitialized = ref(false);
	const currentTransparency = ref(1.0);
	const showPersonDetailPanel = ref(false);
	const selectedPersonData = ref(null);

	let viewEngine = null;
	let characterManager = null;
	const personData = ref([]);
	let fdsData = [];

	// 右键菜单相关状态
	const showContextMenu = ref(false);
	const contextMenuPos = ref({ x: 0, y: 0 });
	const selectedNodeData = ref(null);
	const selectedNodeLabel = ref('');

	// 定义emits
	const emit = defineEmits([
		'scene-loaded',
		'object-click',
		'object-hide',
		'object-show',
		'all-objects-show',
	]);

	// 初始化3D场景
	const initialize = async () => {
		try {
			if (!scene3DContainer.value) {
				console.error('3D容器未找到');
				return;
			}

			console.log('开始初始化3D场景...');

			// 创建ViewEngine实例
			viewEngine = new ViewEngine();

			// 等待DOM更新
			await nextTick();

			// 初始化ViewEngine，并传递容器
			await viewEngine.init(scene3DContainer.value);

			// 设置对象点击回调，实现反向通知
			viewEngine.setObjectClickCallback((nodeData) => {
				console.log('3D对象被点击，通知父组件:', nodeData);
				emit('object-click', nodeData);
			});

			// 获取字符管理器
			characterManager = viewEngine.characterManager;

			// 获取人员数据
			if (characterManager && characterManager.personInfo) {
				personData.value = characterManager.personInfo;
			}

			// 获取FDS数据
			if (viewEngine.fdsJson) {
				fdsData = extractFdsDataFromScene();
			}

			isInitialized.value = true;
			console.log('3D场景初始化完成');

			// 设置右键菜单
			setupContextMenu();

			// 发出场景加载完成事件
			emit('scene-loaded');
			console.log('Scene3D: 发出 scene-loaded 事件');
		} catch (error) {
			console.error('3D场景初始化失败:', error);
			throw error;
		}
	};

	// 从ViewEngine中提取FDS数据
	const extractFdsDataFromScene = () => {
		if (!viewEngine || !viewEngine.fdsJson) {
			return [];
		}

		try {
			// 从ViewEngine的FDS解析数据中获取OBST对象
			const fdsJson = viewEngine.fdsJson;
			const fdsObjects = [];

			// 如果有已处理的分组信息，直接使用
			if (fdsJson.groups && Array.isArray(fdsJson.groups)) {
				console.log(`发现${fdsJson.groups.length}个FDS对象分组`);
				// 收集所有组内对象
				fdsJson.groups.forEach((group) => {
					if (group.items && Array.isArray(group.items)) {
						group.items.forEach((obst) => {
							fdsObjects.push({
								...obst,
								type: 'OBST',
								groupId: group.id,
								groupLabel: group.label,
							});
						});
					}
				});
			} else {
				// 收集所有OBST对象
				if (fdsJson.obst && Array.isArray(fdsJson.obst)) {
					fdsObjects.push(
						...fdsJson.obst.map((obst) => ({
							...obst,
							type: 'OBST',
							// 如果单个对象已有分组信息，保留该信息
							groupId: obst.groupId,
							groupLabel: obst.groupLabel,
						}))
					);
				}

				// 收集所有HOLE对象
				if (fdsJson.hole && Array.isArray(fdsJson.hole)) {
					fdsObjects.push(
						...fdsJson.hole.map((hole) => ({
							...hole,
							type: 'HOLE',
						}))
					);
				}
			}

			// 收集所有SURF对象
			if (fdsJson.surf && Array.isArray(fdsJson.surf)) {
				fdsObjects.push(
					...fdsJson.surf.map((surf) => ({
						...surf,
						type: 'SURF',
					}))
				);
			}

			return fdsObjects.filter((obj) => obj.ID); // 只返回有ID的对象
		} catch (error) {
			console.error('提取FDS数据时出错:', error);
			return [];
		}
	};

	// 获取人员数据
	const getPersonData = () => {
		return personData.value;
	};

	// 获取FDS数据
	const getFdsData = () => {
		return fdsData;
	};

	// 高亮对象（不移动相机）
	const focusOnObject = (nodeData) => {
		console.log('尝试高亮对象:', nodeData);

		if (!viewEngine || !isInitialized.value) {
			console.warn('3D场景未初始化');
			return;
		}

		try {
			// 查找对象
			const object = viewEngine.findObjectByNodeData(nodeData);

			if (object) {
				// 判断是普通对象还是InstancedMesh实例
				if (object.mesh && object.instanceId !== undefined) {
					// 是InstancedMesh实例
					viewEngine.highlightInstance(
						object.mesh,
						object.instanceId,
						object.data
					);
				} else {
					// 是普通对象
					viewEngine.highlightObject(object);
				}
				console.log(`已高亮对象: ${nodeData.label}`);
			} else {
				// 使用ViewEngine的统一高亮系统作为备选
				viewEngine.highlightObjectByNodeData(nodeData);
				console.log(`已尝试高亮对象: ${nodeData.label}`);
			}
		} catch (error) {
			console.error('高亮对象时出错:', error);
		}
	};

	// 处理透明度变化
	const handleTransparencyChange = (value) => {
		currentTransparency.value = value;
		if (viewEngine && viewEngine.setFdsTransparency) {
			viewEngine.setFdsTransparency(value);
		}
	};

	// 处理相机重置
	const handleCameraReset = () => {
		if (viewEngine && viewEngine.resetCamera) {
			viewEngine.resetCamera();
		}
	};

	// 平滑移动相机
	const smoothMoveCamera = (targetX, targetY, targetZ, lookAtTarget) => {
		if (!viewEngine || !viewEngine.camera) return;

		const camera = viewEngine.camera;
		const startPos = camera.position.clone();
		const targetPos = { x: targetX, y: targetY, z: targetZ };

		// 使用简单的lerp动画
		let progress = 0;
		const duration = 1000; // 1秒动画
		const startTime = Date.now();

		const animate = () => {
			const elapsed = Date.now() - startTime;
			progress = Math.min(elapsed / duration, 1);

			// 使用easeInOut缓动函数
			const eased =
				progress < 0.5
					? 2 * progress * progress
					: 1 - Math.pow(-2 * progress + 2, 3) / 2;

			camera.position.lerpVectors(startPos, targetPos, eased);

			if (lookAtTarget) {
				camera.lookAt(lookAtTarget.x, lookAtTarget.y, lookAtTarget.z);
			}

			if (progress < 1) {
				requestAnimationFrame(animate);
			}
		};

		animate();
	};

	// 高性能隐藏单个对象
	const hideObjectById = (objectId) => {
		if (!viewEngine || !isInitialized.value) return false;
		return viewEngine.hideObjectById(objectId);
	};

	// 高性能显示单个对象
	const showObjectById = (objectId) => {
		if (!viewEngine || !isInitialized.value) return false;
		return viewEngine.showObjectById(objectId);
	};

	// 高性能批量隐藏组内对象
	const hideObjectsByGroupId = (groupId) => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.hideObjectsByGroupId(groupId);
	};

	// 高性能批量显示组内对象
	const showObjectsByGroupId = (groupId) => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.showObjectsByGroupId(groupId);
	};

	// 获取组内隐藏对象数量
	const getHiddenCountInGroup = (groupId) => {
		if (!viewEngine || !isInitialized.value) return { hidden: 0, total: 0 };
		return viewEngine.getHiddenCountInGroup(groupId);
	};

	// 检查对象是否隐藏
	const isObjectHidden = (objectId) => {
		if (!viewEngine || !isInitialized.value) return false;
		return viewEngine.isObjectHidden(objectId);
	};

	// 显示所有对象
	const showAllObjects = () => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.showAllObjects();
	};

	// 获取所有隐藏对象ID
	const getHiddenObjectIds = () => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.getHiddenObjectIds();
	};

	// 隐藏所有FDS对象
	const hideAllFdsObjects = () => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.hideAllFdsObjects();
	};

	// 显示所有FDS对象
	const showAllFdsObjects = () => {
		if (!viewEngine || !isInitialized.value) return [];
		return viewEngine.showAllFdsObjects();
	};

	// 获取所有FDS对象隐藏统计
	const getAllFdsHiddenCount = () => {
		if (!viewEngine || !isInitialized.value) return { hidden: 0, total: 0 };
		return viewEngine.getAllFdsHiddenCount();
	};

	// 重置场景
	const resetScene = () => {
		if (!viewEngine) return;

		// 重置视图引擎中的其他状态
		if (viewEngine.clearHighlight) {
			viewEngine.clearHighlight();
		}

		// 发出事件通知父组件
		emit('all-objects-show');
	};

	// 获取角色管理器
	const getCharacterManager = () => {
		console.log('获取角色管理器:', characterManager);
		return characterManager;
	};

	// 获取视图引擎
	const getViewEngine = () => {
		console.log('Scene3D: getViewEngine 被调用', viewEngine);
		return viewEngine;
	};

	// 设置对象透明度
	const setObjectTransparency = (transparency) => {
		if (!viewEngine) return;

		// 使用统一的透明度设置方法
		if (viewEngine.setFdsTransparency) {
			viewEngine.setFdsTransparency(transparency);
		}
	};

	// 重置相机
	const resetCamera = () => {
		if (viewEngine && viewEngine.resetCamera) {
			viewEngine.resetCamera();
		}
	};

	// 设置FDS材质粗糙度
	const setFdsRoughness = (roughness) => {
		if (viewEngine && viewEngine.setFdsRoughness) {
			viewEngine.setFdsRoughness(roughness);
		}
	};

	// 设置FDS材质金属度
	const setFdsMetalness = (metalness) => {
		if (viewEngine && viewEngine.setFdsMetalness) {
			viewEngine.setFdsMetalness(metalness);
		}
	};

	// 设置FDS环境贴图强度
	const setFdsEnvMapIntensity = (intensity) => {
		if (viewEngine && viewEngine.setFdsEnvMapIntensity) {
			viewEngine.setFdsEnvMapIntensity(intensity);
		}
	};

	// 设置环境光强度
	const setAmbientLightIntensity = (intensity) => {
		if (viewEngine && viewEngine.setAmbientLightIntensity) {
			viewEngine.setAmbientLightIntensity(intensity);
		}
	};

	// 设置环境光颜色
	const setAmbientLightColor = (color) => {
		if (viewEngine && viewEngine.setAmbientLightColor) {
			viewEngine.setAmbientLightColor(color);
		}
	};

	// 设置平行光强度
	const setDirectionalLightIntensityChange = (intensity) => {
		if (viewEngine && viewEngine.setDirectionalLightIntensity) {
			viewEngine.setDirectionalLightIntensity(intensity);
		}
	};

	// 设置平行光颜色
	const setDirectionalLightColor = (color) => {
		if (viewEngine && viewEngine.setDirectionalLightColor) {
			viewEngine.setDirectionalLightColor(color);
		}
	};

	// 设置场景亮度级别
	const setSceneBrightness = (level) => {
		if (viewEngine && viewEngine.setSceneBrightness) {
			viewEngine.setSceneBrightness(level);
		}
	};

	// 显示人员详情面板
	const showPersonDetail = (personData) => {
		console.log('显示人员详情面板，人员数据:', personData);
		selectedPersonData.value = personData;
		showPersonDetailPanel.value = true;
	};

	// 隐藏人员详情面板
	const hidePersonDetail = () => {
		showPersonDetailPanel.value = false;
		selectedPersonData.value = null;
	};

	// 设置右键菜单
	const setupContextMenu = () => {
		if (!scene3DContainer.value) return;

		// 添加右键点击事件
		scene3DContainer.value.addEventListener('contextmenu', handleContextMenu);

		// 添加点击事件（用于关闭右键菜单）
		document.addEventListener('click', closeContextMenu);
		window.addEventListener('blur', closeContextMenu);
	};

	// 处理右键菜单
	const handleContextMenu = (event) => {
		event.preventDefault();

		// 如果没有选中对象，不显示右键菜单
		if (!viewEngine || !viewEngine.highlightedObject) return;

		// 获取选中对象的信息
		const highlightedObject = viewEngine.highlightedObject;
		let nodeData;

		// 处理InstancedMesh的情况
		if (highlightedObject.mesh && highlightedObject.instanceId !== undefined) {
			// 从实例数据创建节点数据
			nodeData = viewEngine.createNodeDataFromInstanceData(
				highlightedObject.data
			);

			// 只有对FDS对象显示右键菜单
			if (highlightedObject.data.type !== 'fds') return;

			selectedNodeData.value = nodeData;
			selectedNodeLabel.value = nodeData ? nodeData.label : '未知对象';
		} else {
			// 处理普通对象
			nodeData = viewEngine.createNodeDataFromObject(highlightedObject);

			// 只有对 FDS 对象显示右键菜单
			if (highlightedObject.userData.type !== 'fds') return;

			selectedNodeData.value = nodeData;
			selectedNodeLabel.value = nodeData ? nodeData.label : '未知对象';
		}

		// 设置右键菜单位置
		contextMenuPos.value = {
			x: event.clientX,
			y: event.clientY,
		};

		// 显示右键菜单
		showContextMenu.value = true;
	};

	// 关闭右键菜单
	const closeContextMenu = () => {
		showContextMenu.value = false;
	};

	// 隐藏选中的对象
	const hideSelectedObject = () => {
		if (!viewEngine || !selectedNodeData.value) return;

		try {
			// 获取当前高亮的对象
			const highlightedObject = viewEngine.highlightedObject;

			// 处理InstancedMesh的情况
			if (
				highlightedObject.mesh &&
				highlightedObject.instanceId !== undefined
			) {
				// 设置实例可见性
				viewEngine.setInstanceVisibility(
					highlightedObject.mesh,
					highlightedObject.instanceId,
					false
				);

				console.log(`已隐藏实例对象: ${selectedNodeData.value.label}`);
			}
			// 处理普通对象
			else if (highlightedObject && highlightedObject.userData.type === 'fds') {
				// 隐藏对象
				highlightedObject.visible = false;
				console.log(`已隐藏对象: ${selectedNodeData.value.label}`);
			}

			// 清除高亮
			viewEngine.clearHighlight();

			// 关闭右键菜单
			closeContextMenu();
		} catch (error) {
			console.error('隐藏对象时出错:', error);
		}
	};

	// 对外暴露的方法
	defineExpose({
		initialize,
		focusOnObject,
		getPersonData,
		getFdsData,
		getCharacterManager,
		getViewEngine,

		// 高性能隐藏/显示方法
		hideObjectById,
		showObjectById,
		hideObjectsByGroupId,
		showObjectsByGroupId,
		getHiddenCountInGroup,
		isObjectHidden,
		showAllObjects,
		getHiddenObjectIds,
		hideAllFdsObjects,
		showAllFdsObjects,
		getAllFdsHiddenCount,

		resetCamera,
		resetScene,
		setObjectTransparency,
		setFdsRoughness,
		setFdsMetalness,
		setFdsEnvMapIntensity,
		setAmbientLightIntensity,
		setAmbientLightColor,
		setDirectionalLightIntensityChange,
		setDirectionalLightColor,
		setSceneBrightness,
		showPersonDetail,
		hidePersonDetail,
	});

	onMounted(async () => {
		// 组件挂载后不自动初始化，等待父组件调用
	});

	onUnmounted(() => {
		// 移除右键菜单事件监听
		if (scene3DContainer.value) {
			scene3DContainer.value.removeEventListener(
				'contextmenu',
				handleContextMenu
			);
		}
		document.removeEventListener('click', closeContextMenu);
		window.removeEventListener('blur', closeContextMenu);

		// 清理3D场景
		if (viewEngine) {
			viewEngine.stop();
		}
	});
</script>

<style scoped>
	.scene-3d {
		width: 100%;
		height: 100%;
		position: relative;
		overflow: hidden;
	}

	/* 右键菜单样式 */
	.context-menu {
		position: fixed;
		min-width: 180px;
		z-index: 1001;
		overflow: hidden;
		animation: fadeIn var(--app-duration-fast) var(--app-ease-out);
	}

	.normal-card {
		background: var(--app-surface-1);
		border: 1px solid var(--app-border-primary);
		border-radius: var(--app-radius-large);
		box-shadow: var(--app-shadow-medium);
	}

	.context-menu-header {
		padding: var(--app-space-sm) var(--app-space-md);
		background: var(--app-surface-3);
		border-bottom: 1px solid var(--app-border-primary);
	}

	.context-menu-title {
		font-size: var(--app-font-size-sm);
		font-weight: var(--app-font-weight-medium);
		color: var(--app-text-secondary);
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.context-menu-content {
		padding: var(--app-space-xs) 0;
	}

	.context-menu-item {
		display: flex;
		align-items: center;
		gap: var(--app-space-sm);
		padding: var(--app-space-sm) var(--app-space-md);
		font-size: var(--app-font-size-sm);
		color: var(--app-text-primary);
		cursor: pointer;
		transition: all var(--app-duration-fast);
	}

	.context-menu-item:hover {
		background: var(--app-surface-3);
		color: var(--app-primary);
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.95);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	.scene-main-content {
		width: 100%;
		height: 100%;
		position: relative;
		overflow: hidden;
	}

	/* 确保3D渲染器容器样式 */
	:deep(canvas) {
		display: block;
		outline: none;
		width: 100% !important;
		height: 100% !important;
	}

	/* 响应式设计 */
	@media (max-width: 1200px) {
		.scene-sidebar {
			width: 280px;
		}
	}

	@media (max-width: 768px) {
		.scene-3d {
			flex-direction: column;
		}

		.scene-sidebar {
			width: 100%;
			height: auto;
			border-left: none;
			border-top: 1px solid var(--app-border-primary);
			padding: var(--app-space-sm);
		}
	}
</style>
